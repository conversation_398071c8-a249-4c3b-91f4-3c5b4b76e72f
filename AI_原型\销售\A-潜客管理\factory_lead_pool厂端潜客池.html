<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>厂端潜客池管理 - DMS系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #1890ff;
            --primary-hover: #40a9ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #f5222d;
            --bg-white: #ffffff;
            --bg-light: #fafafa;
            --border-color: #e8e8e8;
            --text-primary: #333333;
            --text-secondary: #666666;
        }

        body {
            font-size: 13px;
            color: var(--text-primary);
            background-color: var(--bg-light);
            margin: 0;
            padding: 0;
            display: flex;
        }

        /* 左侧菜单栏样式 */
        .sidebar {
            width: 180px;
            background-color: #1f2937;
            color: #ffffff;
            min-height: 100vh;
            padding: 0;
            display: flex;
            flex-direction: column;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #374151;
            background-color: #111827;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .sidebar-nav {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: calc(100vh - 80px);
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .sidebar-nav::-webkit-scrollbar {
            display: none;
        }

        .nav-group {
            margin-bottom: 20px;
        }

        .nav-group-title {
            padding: 8px 16px;
            font-size: 10px;
            font-weight: 600;
            color: #9ca3af;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 6px;
        }

        .nav-item {
            display: block;
            padding: 10px 16px;
            color: #d1d5db;
            text-decoration: none;
            transition: all 0.15s ease;
            border-left: 3px solid transparent;
            font-size: 13px;
        }

        .nav-item:hover {
            background-color: #374151;
            color: #ffffff;
            text-decoration: none;
        }

        .nav-item.active {
            background-color: #1f3a8a;
            color: #ffffff;
            border-left-color: #3b82f6;
            font-weight: 500;
        }

        .nav-item i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 180px;
            min-height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
        }

        /* 统计卡片样式 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stats-card {
            background: var(--bg-white);
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        .stats-card h3 {
            margin: 0 0 10px 0;
            font-size: 13px;
            color: var(--text-secondary);
            font-weight: normal;
        }

        .stats-card .number {
            font-size: 24px;
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .stats-card .trend {
            font-size: 12px;
            color: var(--success-color);
        }
        
        .page-container {
            padding: 20px;
            background-color: var(--bg-light);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .page-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            color: var(--text-primary);
            flex-shrink: 0;
        }

        /* 筛选区域 */
        .filter-section {
            background-color: #ffffff;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            flex-shrink: 0;
        }

        .form-label {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .form-control, .form-select {
            font-size: 13px;
            border-color: var(--border-color);
        }

        /* 功能按钮区域 */
        .action-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .btn {
            font-size: 13px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .tab-button {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            color: #6b7280;
            padding: 8px 16px;
            margin-right: 8px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.15s ease;
            cursor: pointer;
        }

        .tab-button:hover {
            background-color: #f9fafb;
            border-color: #9ca3af;
            color: #374151;
        }

        .tab-button.active {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        /* 跨门店关联记录样式 */
        .store-association {
            background: var(--bg-light);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .store-association h4 {
            margin: 0 0 10px 0;
            font-size: 13px;
            color: var(--text-primary);
            font-weight: bold;
        }
        
        .association-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .association-item:last-child {
            border-bottom: none;
        }
        
        .store-info {
            flex: 1;
        }
        
        .store-name {
            font-weight: bold;
            color: var(--text-primary);
            font-size: 13px;
        }
        
        .store-details {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 2px;
        }
        
        .followup-status {
            text-align: right;
        }
        
        .intent-level {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid;
        }
        
        .intent-h { background: #fff2f0; border-color: #ffccc7; color: #ff4d4f; }
        .intent-a { background: #fffbe6; border-color: #ffe58f; color: var(--warning-color); }
        .intent-b { background: #f6ffed; border-color: #b7eb8f; color: var(--success-color); }
        .intent-c { background: #f0f0f0; border-color: #d9d9d9; color: var(--text-secondary); }

        
        /* 可滚动内容区域 */
        .scrollable-content {
            flex: 1;
            overflow-y: auto;
            padding-right: 2px;
            min-height: 0;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .scrollable-content::-webkit-scrollbar {
            display: none;
        }

        /* 表格容器 */
        .table-container {
            background-color: #ffffff;
            width: 100%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
            margin-top: 0px;
        }

        .table-responsive {
            width: 100%;
            margin-bottom: 0px;
            overflow-x: auto;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            display: block;
            max-height: 70vh;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .table-responsive::-webkit-scrollbar {
            display: none;
        }

        .table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
            margin-bottom: 0;
            white-space: nowrap;
            font-size: 13px;
            background-color: #ffffff;
            min-width: 1200px;
        }

        @media (max-width: 1500px) {
            .table-responsive {
                min-width: 100%;
            }

            .table {
                min-width: 1200px;
            }
        }

        .table th {
            background-color: #f9fafb;
            color: #374151;
            font-size: 12px;
            font-weight: 500;
            border-bottom: none;
            padding: 12px 16px;
            text-align: left;
            letter-spacing: 0.025em;
            text-transform: uppercase;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .table td {
            font-size: 12px;
            border-bottom: 1px solid #f3f4f6;
            padding: 10px 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: middle;
            background-color: #ffffff;
            color: #111827;
            height: 48px;
        }

        .table tbody tr {
            transition: background-color 0.15s ease;
        }

        .table tbody tr:hover {
            background-color: #f9fafb;
        }

        .table tbody tr:hover td {
            background-color: #f9fafb;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .row-selected {
            background-color: #e6f7ff;
        }

        /* 固定列样式 */
        .sticky-col {
            position: sticky;
            right: 0;
            background-color: #f9fafb !important;
            z-index: 10;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05);
            border-left: 1px solid #e5e7eb !important;
            overflow: visible !important;
        }

        .first-sticky-col {
            position: sticky;
            left: 0;
            background-color: #ffffff !important;
            z-index: 20;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
        }

        .table th.sticky-col, .table th.first-sticky-col {
            background-color: #f9fafb !important;
            z-index: 25;
            position: sticky;
        }

        .table th.sticky-col, .table td.sticky-col {
            text-align: center !important;
            min-width: 140px !important;
            position: sticky !important;
            right: 0 !important;
            background-color: #ffffff !important;
            z-index: 20 !important;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05);
        }

        .table th.sticky-col {
            background-color: #f9fafb !important;
            z-index: 25 !important;
        }

        .table th.first-sticky-col {
            text-align: center !important;
        }

        .table td.first-sticky-col {
            text-align: center !important;
            background-color: #ffffff !important;
        }

        .table tbody tr:hover td.sticky-col {
            background-color: #f9fafb !important;
        }

        .table tbody tr:hover td.first-sticky-col {
            background-color: #f9fafb !important;
        }

        .row-selected td.sticky-col {
            background-color: #e0e7ff !important;
        }

        .row-selected td.first-sticky-col {
            background-color: #e0f2fe !important;
        }

        .col-checkbox {
            width: 50px;
            min-width: 50px !important;
        }

        .col-seq {
            width: 60px;
            min-width: 60px !important;
        }

        /* 标签样式 */
        .tag {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            letter-spacing: 0.025em;
        }

        .tag-success {
            background-color: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .tag-warning {
            background-color: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .tag-error {
            background-color: #fee2e2;
            color: #dc2626;
            border: 1px solid #fca5a5;
        }

        .tag-primary {
            background-color: #dbeafe;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }

        .tag-info {
            background-color: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        /* 分页区域 */
        .pagination-section {
            background-color: #ffffff;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #e5e7eb;
            border-radius: 0 0 8px 8px;
        }

        .page-button {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            color: #374151;
            padding: 8px 12px;
            margin: 0 2px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.15s ease;
        }

        .page-button:hover:not(.disabled) {
            background-color: #f3f4f6;
            border-color: #9ca3af;
        }

        .page-button.active {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .page-button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background-color: #f9fafb;
            color: #9ca3af;
        }

        .btn-sm {
            padding: 6px 8px;
            font-size: 12px;
            margin: 1px;
            border-radius: 4px;
            font-weight: 500;
            border: 1px solid;
            transition: all 0.15s ease;
            width: 28px;
            height: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .sticky-col .btn-sm {
            display: inline-flex;
        }

        .btn-success {
            background-color: #10b981;
            border-color: #10b981;
            color: white;
        }

        .btn-success:hover {
            background-color: #059669;
            border-color: #059669;
        }

        .btn-danger {
            background-color: #ef4444;
            border-color: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background-color: #dc2626;
            border-color: #dc2626;
        }

        .btn-primary {
            background-color: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
            border-color: #2563eb;
        }

        /* Tooltip样式 */
        .btn-tooltip {
            position: relative;
        }

        .btn-tooltip::after {
            content: attr(data-tooltip);
            position: fixed;
            top: 0;
            left: 0;
            transform: translateX(-50%);
            background-color: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s;
            z-index: 999999999;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            pointer-events: none;
        }

        .btn-tooltip::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: #1f2937;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s;
            z-index: 999999999;
            pointer-events: none;
        }

        .btn-tooltip:hover::after,
        .btn-tooltip:hover::before {
            opacity: 1;
            visibility: visible;
        }

        .btn-tooltip:hover::after {
            top: calc(var(--tooltip-top) - 45px);
            left: var(--tooltip-left);
        }

        .btn-tooltip:hover::before {
            top: calc(var(--tooltip-top) - 12px);
            left: var(--tooltip-left);
        }

        /* 详情模态框样式 */
        .modal-large {
            max-width: 1200px;
        }

        .modal-title {
            font-size: 13px;
            font-weight: bold;
        }

        .modal-dialog {
            max-width: 800px;
        }

        .modal-dialog.modal-lg {
            max-width: 1000px;
        }
        
        .detail-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }
        
        .detail-tab {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 13px;
            color: var(--text-secondary);
            border-bottom: 2px solid transparent;
        }
        
        .detail-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }
        
        .detail-content {
            display: none;
        }
        
        .detail-content.active {
            display: block;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
        }
        
        .info-label {
            font-weight: bold;
            color: var(--text-primary);
            min-width: 80px;
            font-size: 13px;
        }
        
        .info-value {
            color: var(--text-secondary);
            font-size: 13px;
        }

        .sensitive-info {
            background-color: #f0f0f0;
            border-radius: 2px;
            padding: 0 4px;
            color: #666;
        }

        .info-note {
            font-size: 12px;
            color: #999;
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <!-- 左侧菜单栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>
                <i class="bi bi-speedometer2 me-2"></i>
                Perodua DMS系统
            </h3>
        </div>
        <nav class="sidebar-nav">
            <div class="nav-group">
                <div class="nav-group-title">销售管理</div>
                <a href="#" class="nav-item active">
                    <i class="bi bi-people"></i>
                    潜客管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-key"></i>
                    试驾管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-clipboard-check"></i>
                    销售订单管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-credit-card"></i>
                    付款/退款管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-receipt"></i>
                    发票管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-truck"></i>
                    交车管理
                </a>
            </div>
            <div class="nav-group">
                <div class="nav-group-title">售后管理</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-calendar-check"></i>
                    预约管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-file-earmark-text"></i>
                    工单管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-tools"></i>
                    派工管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-shield-check"></i>
                    质检管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-calculator"></i>
                    结算管理
                </a>
            </div>
            <div class="nav-group">
                <div class="nav-group-title">配件管理</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-boxes"></i>
                    库存管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-arrow-down-circle"></i>
                    入库管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-arrow-up-circle"></i>
                    出库管理
                </a>
            </div>
            <div class="nav-group">
                <div class="nav-group-title">系统管理</div>
                <a href="#" class="nav-item">
                    <i class="bi bi-person-gear"></i>
                    用户管理
                </a>
                <a href="#" class="nav-item">
                    <i class="bi bi-gear"></i>
                    系统设置
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="page-container">
            <h1 class="page-title">厂端潜客池管理</h1>
        
        <!-- 统计概览 -->
        <div class="stats-container">
            <div class="stats-card">
                <h3>总潜客数</h3>
                <div class="number">12,456</div>
                <div class="trend">较上月 +8.5%</div>
            </div>
            <div class="stats-card">
                <h3>H级潜客</h3>
                <div class="number">2,341</div>
                <div class="trend">较上月 +12.3%</div>
            </div>
            <div class="stats-card">
                <h3>本月成交</h3>
                <div class="number">856</div>
                <div class="trend">成交率 6.9%</div>
            </div>
            <div class="stats-card">
                <h3>跨门店潜客</h3>
                <div class="number">1,234</div>
                <div class="trend">占比 9.9%</div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <form id="filterForm">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">潜客ID</label>
                        <input type="text" class="form-control" id="leadId" placeholder="请输入潜客ID">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">门店</label>
                        <select class="form-select" id="store">
                            <option value="">全部门店</option>
                            <option value="store1">Perodua Sentral</option>
                            <option value="store2">PSSB Glenmarie</option>
                            <option value="store3">PSSB Putrajaya</option>
                            <option value="store4">PSSB JB1</option>
                            <option value="store5">PSSB Juru</option>
                            <option value="store6">PSSB Senawang</option>
                            <option value="store7">PSSB Ipoh 2</option>
                            <option value="store8">PSSB KB1</option>
                            <option value="store9">PSSB Kuantan</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">关联门店数</label>
                        <select class="form-select" id="storeCount">
                            <option value="">全部</option>
                            <option value="1">单店</option>
                            <option value="multiple">多店</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">注册时间</label>
                        <div class="d-flex align-items-center">
                            <input type="date" class="form-control" id="startDate" value="2024-01-01" style="flex: 1 1 0%; min-width: 0; margin-right: 5px;">
                            <span>至</span>
                            <input type="date" class="form-control" id="endDate" value="2024-12-31" style="flex: 1 1 0%; min-width: 0; margin-left: 5px;">
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 可滚动内容区域 -->
        <div class="scrollable-content">
            <!-- 功能按钮区域 -->
            <div class="action-section">
                <div>
                    <button type="button" class="btn btn-primary me-2" onclick="document.getElementById('filterForm').dispatchEvent(new Event('submit'))">
                        <i class="bi bi-search"></i> 查询
                    </button>
                    <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="exportData()">
                        <i class="bi bi-download"></i> 导出数据
                    </button>
                </div>
                <div>
                    <button class="tab-button active" onclick="filterByType('all')">全部潜客</button>
                    <button class="tab-button" onclick="filterByType('cross')">跨门店潜客</button>
                    <button class="tab-button" onclick="filterByType('failed')">战败潜客</button>
                    <button class="tab-button" onclick="filterByType('converted')">已成交</button>
                </div>
            </div>

            <!-- 潜客列表 -->
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th class="col-seq first-sticky-col" style="text-align: center; width: 60px;">序号</th>
                                <th style="width: 120px;">潜客ID</th>
                                <th style="width: 100px;">潜客姓名</th>
                                <th style="width: 140px;">手机号</th>
                                <th style="width: 120px;">关联门店数</th>
                                <th style="width: 180px;">注册时间</th>
                                <th style="width: 120px; text-align: center;">成交状态</th>
                                <th style="width: 140px; text-align: center;" class="sticky-col">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-seq first-sticky-col">1</td>
                                <td>CL202401001</td>
                                <td>Ahmad R***</td>
                                <td>012-345-****</td>
                                <td><span class="tag tag-warning">3家门店</span></td>
                                <td>2024-01-10 09:15</td>
                                <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                                <td class="sticky-col">
                                    <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead1')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-seq first-sticky-col">2</td>
                                <td>CL202401002</td>
                                <td>Lim W***</td>
                                <td>013-456-****</td>
                                <td><span class="tag tag-info">1家门店</span></td>
                                <td>2024-01-08 11:20</td>
                                <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                                <td class="sticky-col">
                                    <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead2')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-seq first-sticky-col">3</td>
                                <td>CL202401003</td>
                                <td>Siti N***</td>
                                <td>014-567-****</td>
                                <td><span class="tag tag-warning">2家门店</span></td>
                                <td>2024-01-05 15:30</td>
                                <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                                <td class="sticky-col">
                                    <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead3')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-seq first-sticky-col">4</td>
                                <td>CL202401004</td>
                                <td>Raj K***</td>
                                <td>016-789-****</td>
                                <td><span class="tag tag-info">1家门店</span></td>
                                <td>2024-01-03 13:45</td>
                                <td style="text-align: center;"><span class="tag tag-success">已成交</span></td>
                                <td class="sticky-col">
                                    <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead4')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-seq first-sticky-col">5</td>
                                <td>CL202312001</td>
                                <td>Tan A***</td>
                                <td>017-890-****</td>
                                <td><span class="tag tag-warning">2家门店</span></td>
                                <td>2023-12-20 10:30</td>
                                <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                                <td class="sticky-col">
                                    <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead5')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页区域 -->
                <div class="pagination-section">
                    <div>
                        <span>每页显示</span>
                        <select class="form-select d-inline-block mx-2" style="width: auto;" onchange="changePageSize(this.value)">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>条，共 <span id="totalCount">12,456</span> 条记录</span>
                    </div>
                    <div>
                        <button class="page-button disabled" id="prevPage" onclick="changePage('prev')">上一页</button>
                        <button class="page-button active" id="currentPage">1</button>
                        <button class="page-button" onclick="changePage('next')">2</button>
                        <button class="page-button">3</button>
                        <button class="page-button">...</button>
                        <button class="page-button">623</button>
                        <button class="page-button" id="nextPage" onclick="changePage('next')">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- 潜客详情模态框 -->
    <div class="modal fade" id="leadDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">潜客详情 - CL202401001 Ahmad R***</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
            <div class="modal-body">
                <!-- 详情标签页 -->
                <div class="detail-tabs">
                    <button class="detail-tab active" onclick="showDetailTab('basic')">基本信息</button>
                    <button class="detail-tab" onclick="showDetailTab('stores')">门店关联</button>
                    <button class="detail-tab" onclick="showDetailTab('followup')">跟进记录</button>
                    <button class="detail-tab" onclick="showDetailTab('testdrive')">试驾记录</button>
                    <button class="detail-tab" onclick="showDetailTab('failed')">战败记录</button>
                    <button class="detail-tab" onclick="showDetailTab('history')">变更历史</button>
                    <button class="detail-tab" onclick="showDetailTab('analytics')">效果分析</button>
                </div>

                <!-- 基本信息 -->
                <div class="detail-content active" id="basic">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">潜客ID:</span>
                            <span class="info-value">CL202401001</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">潜客姓名:</span>
                            <span class="info-value">Ahmad R***</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">手机号:</span>
                            <span class="info-value">012-345-****</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">身份证件类型:</span>
                            <span class="info-value">MyKad (IC)</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">证件号码:</span>
                            <span class="info-value">901201-**-****</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">邮箱:</span>
                            <span class="info-value">ahmad.r***@email.com</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">注册时间:</span>
                            <span class="info-value">2024-01-10 09:15:30</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">注册来源:</span>
                            <span class="info-value">Super APP</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">当前状态:</span>
                            <span class="info-value"><span class="tag">未成交</span></span>
                        </div>
                    </div>
                </div>

                <!-- 门店关联 -->
                <div class="detail-content" id="stores">
                    <div class="store-association">
                        <h4>关联门店记录 (共3家门店)</h4>
                        <div class="association-item">
                            <div class="store-info">
                                <div class="store-name">Perodua Sentral</div>
                                <div class="store-details">关联时间: 2024-01-10 09:20 | 关联原因: 自动分配 | 当前顾问: Lim Wei Ming</div>
                            </div>
                            <div class="followup-status">
                                <div><span class="intent-level intent-h">H级</span></div>
                                <div style="font-size: 12px; color: #666; margin-top: 2px;">最后跟进: 2024-01-15 14:30</div>
                            </div>
                        </div>
                        <div class="association-item">
                            <div class="store-info">
                                <div class="store-name">PSSB Glenmarie</div>
                                <div class="store-details">关联时间: 2024-01-12 15:30 | 关联原因: 客户到店 | 当前顾问: Siti Aminah</div>
                            </div>
                            <div class="followup-status">
                                <div><span class="intent-level intent-a">A级</span></div>
                                <div style="font-size: 12px; color: #666; margin-top: 2px;">最后跟进: 2024-01-14 10:15</div>
                            </div>
                        </div>
                        <div class="association-item">
                            <div class="store-info">
                                <div class="store-name">PSSB JB1</div>
                                <div class="store-details">关联时间: 2024-01-13 11:45 | 关联原因: 试驾预约 | 当前顾问: 未分配</div>
                            </div>
                            <div class="followup-status">
                                <div><span class="intent-level intent-c">C级</span></div>
                                <div style="font-size: 12px; color: #666; margin-top: 2px;">低意向，需要持续跟进</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 跟进记录 -->
                <div class="detail-content" id="followup">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="120px">门店</th>
                                    <th width="100px">销售顾问</th>
                                    <th width="100px">跟进方式</th>
                                    <th width="120px">跟进时间</th>
                                    <th width="100px">意向级别</th>
                                    <th>跟进情况</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Perodua Sentral</td>
                                    <td>Lim Wei Ming</td>
                                    <td>电话</td>
                                    <td>2024-01-15 14:30</td>
                                    <td><span class="intent-level intent-h">H级</span></td>
                                    <td>客户表示有购车意向，计划本周末到店看车</td>
                                </tr>
                                <tr>
                                    <td>PSSB Glenmarie</td>
                                    <td>Siti Aminah</td>
                                    <td>到店</td>
                                    <td>2024-01-14 10:15</td>
                                    <td><span class="intent-level intent-a">A级</span></td>
                                    <td>客户到店看车，对车型比较满意，需要考虑价格</td>
                                </tr>
                                <tr>
                                    <td>Perodua Sentral</td>
                                    <td>Lim Wei Ming</td>
                                    <td>WhatsApp</td>
                                    <td>2024-01-12 16:20</td>
                                    <td><span class="intent-level intent-h">H级</span></td>
                                    <td>发送车型资料，客户回复感兴趣</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 试驾记录 -->
                <div class="detail-content" id="testdrive">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="120px">门店</th>
                                    <th width="100px">试驾人</th>
                                    <th width="120px">手机号</th>
                                    <th width="120px">试驾车型Model</th>
                                    <th width="120px">配置Variant</th>
                                    <th width="120px">试驾时间</th>
                                    <th>试驾反馈</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Perodua Sentral</td>
                                    <td>Ahmad R***</td>
                                    <td>012-345-****</td>
                                    <td>MYVI</td>
                                    <td>1.5L H CVT</td>
                                    <td>2024-01-13 10:30</td>
                                    <td>对车型满意，驾驶感受良好，对价格有些犹豫</td>
                                </tr>
                                <tr>
                                    <td>PSSB Glenmarie</td>
                                    <td>Ahmad R***</td>
                                    <td>012-345-****</td>
                                    <td>MYVI</td>
                                    <td>1.5L X CVT</td>
                                    <td>2024-01-14 14:15</td>
                                    <td>对X型配置更满意，计划再考虑一下</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 战败记录 -->
                <div class="detail-content" id="failed">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="120px">申请时间</th>
                                    <th width="100px">门店</th>
                                    <th width="100px">申请人</th>
                                    <th width="120px">审核状态</th>
                                    <th width="120px">审核时间</th>
                                    <th width="100px">审核人</th>
                                    <th>战败原因</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-02 14:00</td>
                                    <td>PSSB JB1</td>
                                    <td>Kumar Raj</td>
                                    <td><span class="tag tag-success">审核通过</span></td>
                                    <td>2024-01-02 16:30</td>
                                    <td>Ahmad Manager</td>
                                    <td>客户表示预算不足，决定购买其他品牌车型</td>
                                </tr>
                                <tr>
                                    <td>2023-12-25 09:15</td>
                                    <td>Perodua Sentral</td>
                                    <td>Lim Wei Ming</td>
                                    <td><span class="tag tag-error">审核驳回</span></td>
                                    <td>2023-12-25 11:20</td>
                                    <td>Tan Manager</td>
                                    <td>跟进不够充分，需要继续尝试联系客户</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 变更历史 -->
                <div class="detail-content" id="history">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="120px">变更时间</th>
                                    <th width="100px">门店</th>
                                    <th width="100px">变更类型</th>
                                    <th width="100px">操作人</th>
                                    <th width="120px">变更前</th>
                                    <th width="120px">变更后</th>
                                    <th>变更原因</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-15 14:35</td>
                                    <td>Perodua Sentral</td>
                                    <td>意向级别</td>
                                    <td>Lim Wei Ming</td>
                                    <td><span class="intent-level intent-a">A级</span></td>
                                    <td><span class="intent-level intent-h">H级</span></td>
                                    <td>客户表示强烈购车意向</td>
                                </tr>
                                <tr>
                                    <td>2024-01-11 10:00</td>
                                    <td>Perodua Sentral</td>
                                    <td>顾问分配</td>
                                    <td>Tan Manager</td>
                                    <td>未分配</td>
                                    <td>Lim Wei Ming</td>
                                    <td>销售经理手动分配</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 效果分析 -->
                <div class="detail-content" id="analytics">
                    <div class="stats-container">
                        <div class="stats-card">
                            <h3>跟进总次数</h3>
                            <div class="number">8</div>
                            <div class="trend">跨3家门店</div>
                        </div>
                        <div class="stats-card">
                            <h3>最活跃门店</h3>
                            <div class="number">Perodua Sentral</div>
                            <div class="trend">跟进5次</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 意向级别配置模态框 -->
    <div class="modal fade" id="intentLevelConfigModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">意向级别配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info" role="alert">
                        <i class="bi bi-info-circle me-2"></i>
                        规则变更只对变更后新设置的意向级别生效，不影响已存在的潜客
                    </div>
                    <form id="intentLevelConfigForm">
                        <div class="mb-3">
                            <label class="form-label fw-bold">H级 - 高意向潜客</label>
                            <div class="input-group mb-2">
                                <span class="input-group-text">跟进时限</span>
                                <input type="number" class="form-control" value="24" min="1" max="240">
                                <span class="input-group-text">小时</span>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">定义描述</label>
                                <textarea class="form-control" rows="2">高意向潜客，明确表示近期有购车计划，并已有明确预算。</textarea>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">A级 - 较高意向潜客</label>
                            <div class="input-group mb-2">
                                <span class="input-group-text">跟进时限</span>
                                <input type="number" class="form-control" value="72" min="1" max="240">
                                <span class="input-group-text">小时</span>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">定义描述</label>
                                <textarea class="form-control" rows="2">较高意向潜客，有明确购买意向但暂未决定，需要继续跟进。</textarea>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">B级 - 中等意向潜客</label>
                            <div class="input-group mb-2">
                                <span class="input-group-text">跟进时限</span>
                                <input type="number" class="form-control" value="168" min="1" max="720">
                                <span class="input-group-text">小时</span>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">定义描述</label>
                                <textarea class="form-control" rows="2">中等意向潜客，有一定购买意向但时间不确定，需要持续维护关系。</textarea>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">C级 - 低意向潜客</label>
                            <div class="input-group mb-2">
                                <span class="input-group-text">跟进时限</span>
                                <input type="number" class="form-control" value="720" min="1" max="1440">
                                <span class="input-group-text">小时</span>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">定义描述</label>
                                <textarea class="form-control" rows="2">低意向潜客，近期无购买打算，但有长期购买可能性。</textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveIntentLevelConfig()">保存配置</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示潜客详情模态框
        function showLeadDetail(leadId) {
            const modal = new bootstrap.Modal(document.getElementById('leadDetailModal'));
            modal.show();
        }

        // 切换详情标签页
        function showDetailTab(tabName) {
            // 隐藏所有内容
            const contents = document.querySelectorAll('.detail-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的激活状态
            const tabs = document.querySelectorAll('.detail-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活选中的标签
            event.target.classList.add('active');
        }

        // 重置表单
        function resetForm() {
            document.getElementById('filterForm').reset();
            showToast('筛选条件已重置', 'success');
        }

        // 导出数据
        function exportData() {
            showToast('正在导出数据，请稍候...', 'success');
        }

        // 筛选类型
        function filterByType(type) {
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            const typeText = type === 'all' ? '全部潜客' :
                             type === 'cross' ? '跨门店潜客' :
                             type === 'failed' ? '战败潜客' : '已成交潜客';
            
            // 更新表格数据以匹配选择的类型
            const tbody = document.querySelector('table tbody');
            tbody.innerHTML = ''; // 清空现有数据
            
            // 根据类型加载对应数据
            if (type === 'all') {
                // 添加所有潜客数据
                loadAllLeads();
            } else if (type === 'cross') {
                // 添加跨门店潜客数据
                loadCrossStoreLeads();
            } else if (type === 'failed') {
                // 添加战败潜客数据
                loadFailedLeads();
            } else if (type === 'converted') {
                // 添加已转化潜客数据
                loadConvertedLeads();
            }
            
            showToast(`已切换到${typeText}视图`, 'success');
        }

        // 加载全部潜客数据
        function loadAllLeads() {
            const tbody = document.querySelector('table tbody');
            // 这里使用示例数据，实际应用中应该从API获取
            tbody.innerHTML = `
                <tr>
                    <td class="col-seq first-sticky-col">1</td>
                    <td>CL202401001</td>
                    <td>张**</td>
                    <td>138****5678</td>
                    <td><span class="tag tag-warning">3家门店</span></td>
                    <td>2024-01-10 09:15</td>
                    <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead1')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="col-seq first-sticky-col">2</td>
                    <td>CL202401002</td>
                    <td>李**</td>
                    <td>139****4321</td>
                    <td><span class="tag tag-info">1家门店</span></td>
                    <td>2024-01-08 11:20</td>
                    <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead2')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="col-seq first-sticky-col">3</td>
                    <td>CL202401003</td>
                    <td>Siti N***</td>
                    <td>014-567-****</td>
                    <td><span class="tag tag-warning">2家门店</span></td>
                    <td>2024-01-05 15:30</td>
                    <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead3')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="col-seq first-sticky-col">4</td>
                    <td>CL202401004</td>
                    <td>Raj K***</td>
                    <td>016-789-****</td>
                    <td><span class="tag tag-info">1家门店</span></td>
                    <td>2024-01-03 13:45</td>
                    <td style="text-align: center;"><span class="tag tag-success">已成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead4')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="col-seq first-sticky-col">5</td>
                    <td>CL202312001</td>
                    <td>Tan A***</td>
                    <td>017-890-****</td>
                    <td><span class="tag tag-warning">2家门店</span></td>
                    <td>2023-12-20 10:30</td>
                    <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead5')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        }

        // 加载跨门店潜客数据
        function loadCrossStoreLeads() {
            const tbody = document.querySelector('table tbody');
            tbody.innerHTML = `
                <tr>
                    <td class="col-seq first-sticky-col">1</td>
                    <td>CL202401001</td>
                    <td>Ahmad R***</td>
                    <td>012-345-****</td>
                    <td><span class="tag tag-warning">3家门店</span></td>
                    <td>2024-01-10 09:15</td>
                    <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead1')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="col-seq first-sticky-col">3</td>
                    <td>CL202401003</td>
                    <td>Siti N***</td>
                    <td>014-567-****</td>
                    <td><span class="tag tag-warning">2家门店</span></td>
                    <td>2024-01-05 15:30</td>
                    <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead3')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="col-seq first-sticky-col">5</td>
                    <td>CL202312001</td>
                    <td>Tan A***</td>
                    <td>017-890-****</td>
                    <td><span class="tag tag-warning">2家门店</span></td>
                    <td>2023-12-20 10:30</td>
                    <td style="text-align: center;"><span class="tag tag-info">未成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead5')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        }

        // 加载战败潜客数据
        function loadFailedLeads() {
            const tbody = document.querySelector('table tbody');
            tbody.innerHTML = `
                <tr>
                    <td class="col-seq first-sticky-col">1</td>
                    <td>CL202311005</td>
                    <td>Hassan A***</td>
                    <td>019-234-****</td>
                    <td><span class="tag tag-info">1家门店</span></td>
                    <td>2023-11-15 14:20</td>
                    <td style="text-align: center;"><span class="tag tag-error">战败</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead6')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="col-seq first-sticky-col">2</td>
                    <td>CL202310010</td>
                    <td>Wong K***</td>
                    <td>018-345-****</td>
                    <td><span class="tag tag-info">1家门店</span></td>
                    <td>2023-10-08 09:30</td>
                    <td style="text-align: center;"><span class="tag tag-error">战败</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead7')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        }

        // 加载已转化潜客数据
        function loadConvertedLeads() {
            const tbody = document.querySelector('table tbody');
            tbody.innerHTML = `
                <tr>
                    <td class="col-seq first-sticky-col">1</td>
                    <td>CL202401004</td>
                    <td>Raj K***</td>
                    <td>016-789-****</td>
                    <td><span class="tag tag-info">1家门店</span></td>
                    <td>2024-01-03 13:45</td>
                    <td style="text-align: center;"><span class="tag tag-success">已成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead4')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="col-seq first-sticky-col">2</td>
                    <td>CL202312005</td>
                    <td>Fatimah Z***</td>
                    <td>015-678-****</td>
                    <td><span class="tag tag-info">1家门店</span></td>
                    <td>2023-12-15 16:10</td>
                    <td style="text-align: center;"><span class="tag tag-success">已成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead8')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td class="col-seq first-sticky-col">3</td>
                    <td>CL202312010</td>
                    <td>Kumar M***</td>
                    <td>017-123-****</td>
                    <td><span class="tag tag-warning">2家门店</span></td>
                    <td>2023-12-05 11:25</td>
                    <td style="text-align: center;"><span class="tag tag-success">已成交</span></td>
                    <td class="sticky-col">
                        <button class="btn btn-success btn-sm btn-tooltip" data-tooltip="查看详情" onclick="showLeadDetail('lead9')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        }

        // 分页功能
        function changePage(direction) {
            const currentPage = document.getElementById('currentPage');
            let pageNum = parseInt(currentPage.textContent);
            
            if (direction === 'prev' && pageNum > 1) {
                pageNum--;
            } else if (direction === 'next') {
                pageNum++;
            }
            
            currentPage.textContent = pageNum;
            showToast(`已切换到第 ${pageNum} 页`, 'success');
        }
 
        function changePageSize(size) {
            showToast(`每页显示条数已更改为 ${size} 条`, 'success');
        }
 
        // Toast提示功能
        function showToast(message, type = 'success') {
            // 简单的提示实现
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'warning'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                </div>
            `;
            
            document.body.appendChild(toast);
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
 
        // 初始化tooltip功能
        function initTooltips() {
            document.querySelectorAll('.btn-tooltip').forEach(function(btn) {
                btn.addEventListener('mouseenter', function() {
                    const rect = btn.getBoundingClientRect();
                    const tooltip = btn;
                    tooltip.style.setProperty('--tooltip-top', rect.top + 'px');
                    tooltip.style.setProperty('--tooltip-left', (rect.left + rect.width / 2) + 'px');
                });
            });
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选表单提交
            document.getElementById('filterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                showToast('查询完成', 'success');
            });

            // 初始加载全部潜客数据
            loadAllLeads();

            // 初始化tooltip
            initTooltips();

            // 当表格内容更新时重新初始化tooltip
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        initTooltips();
                    }
                });
            });

            // 观察表格内容变化
            const tableBody = document.querySelector('table tbody');
            if (tableBody) {
                observer.observe(tableBody, { childList: true, subtree: true });
            }

            // 显示欢迎提示
            setTimeout(() => {
                showToast('厂端潜客池管理页面加载完成', 'success');
            }, 500);
        });

        // 保存意向级别配置
        function saveIntentLevelConfig() {
            showToast('意向级别配置已保存', 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('intentLevelConfigModal'));
            modal.hide();
        }
    </script>
</body>
</html>